# 教师评价问卷系统

## 📋 项目概述

基于 Midway.js + TypeScript + Sequelize + MySQL 构建的教师评价问卷系统，提供完整的问卷管理、响应提交和统计分析功能。

## ✨ 核心功能

- 🗂️ **问卷管理**：创建、编辑、发布问卷，支持5星/10星评分模式
- 📝 **问卷填写**：家长提交教师评价，支持重复提交防护和SSO验证
- 📊 **统计分析**：学校和教师维度的多层次数据分析
- 🔐 **SSO集成**：与兴教云SSO系统集成，确保数据安全

## 🚀 快速开始

### 环境要求

- Node.js >= 14.0.0
- MySQL >= 5.7
- Redis (可选，用于缓存)

### 安装依赖

```bash
npm install
```

### 配置数据库

```typescript
// src/config/config.local.ts
export default {
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '127.0.0.1',
        port: 3306,
        username: 'root',
        password: 'your-password',
        database: 'teacher-evaluation-question'
      }
    }
  }
}
```

### 启动项目

```bash
# 开发模式
npm run dev

# 生产模式
npm run build
npm start

# 测试
npm test
```

## 📡 API 接口清单

### 🗂️ 问卷管理模块

| 接口 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 创建问卷 | POST | `/api/questionnaire` | 创建新的问卷 |
| 获取问卷列表 | GET | `/api/questionnaire` | 分页获取问卷列表 |
| 获取问卷详情 | GET | `/api/questionnaire/:id` | 根据ID获取问卷详情 |
| 更新问卷状态 | PUT | `/api/questionnaire/:id/status` | 更新问卷状态（发布/关闭） |
| 删除问卷 | DELETE | `/api/questionnaire/:id` | 删除问卷 |

**创建问卷示例：**
```json
POST /api/questionnaire
{
  "title": "2024年1月教师评价问卷",
  "description": "请对本月教师表现进行评价",
  "month": "2024-01",
  "sso_school_id": "school_001",
  "star_mode": 5,
  "include_school_evaluation": true
}
```

### 📝 问卷填写模块

| 接口 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 提交问卷响应 | POST | `/api/response` | 家长提交教师评价 |
| 获取响应列表 | GET | `/api/response` | 分页获取响应列表 |
| 获取响应详情 | GET | `/api/response/:id` | 根据ID获取响应详情 |
| 检查重复提交 | GET | `/api/response/check` | 检查是否已提交响应 |
| 获取问卷统计 | GET | `/api/response/statistics/:id` | 获取问卷统计信息 |
| 获取评分信息 | GET | `/api/response/rating-info/:id` | 获取星级评分转换信息 |

**提交问卷响应示例：**
```json
POST /api/response
{
  "questionnaire_id": 1,
  "parent_phone": "13800138000",
  "parent_name": "张三家长",
  "sso_student_id": "student_001",
  "month": "2024-01",
  "school_rating": 5,
  "school_description": "学校整体表现很好",
  "teacher_evaluations": [
    {
      "sso_teacher_id": "teacher_001",
      "rating": 5,
      "description": "老师教学认真负责",
      "tags": ["认真", "负责", "专业"],
      "is_recommended": true,
      "teaching_quality_rating": 5,
      "teaching_attitude_rating": 5,
      "classroom_management_rating": 4,
      "communication_rating": 5,
      "professional_knowledge_rating": 5
    }
  ]
}
```

### 📊 统计分析模块

#### 学校维度统计

| 接口 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 学校统计概览 | GET | `/api/statistics/school` | 获取学校整体统计数据 |
| 学校响应趋势 | GET | `/api/statistics/school/:schoolId/trend` | 获取学校响应趋势数据 |
| 教师排名 | GET | `/api/statistics/teacher-ranking` | 获取教师评分排名 |
| 趋势分析 | GET | `/api/statistics/trend` | 获取趋势分析数据 |

**学校统计示例：**
```http
GET /api/statistics/school?sso_school_id=school_001&month=2024-01&include_trend=true&include_teacher_ranking=true
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "获取学校统计数据成功",
  "data": {
    "sso_school_id": "school_001",
    "sso_school_name": "示例小学",
    "total_responses": 150,
    "completed_responses": 142,
    "completion_rate": 94.67,
    "school_average_score": 87.5,
    "teacher_average_score": 89.2,
    "total_teachers_evaluated": 25,
    "response_trend": [...],
    "teacher_ranking": [...]
  }
}
```

#### 教师维度统计

| 接口 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 教师统计概览 | GET | `/api/statistics/teacher` | 获取教师个人统计数据 |
| 教师评分分布 | GET | `/api/statistics/teacher/:teacherId/distribution` | 获取教师评分分布 |
| 教师关键词云 | GET | `/api/statistics/teacher/:teacherId/keywords` | 获取教师评价关键词 |
| 教师评价趋势 | GET | `/api/statistics/teacher/:teacherId/trend` | 获取教师评价趋势 |

**教师统计示例：**
```http
GET /api/statistics/teacher?sso_teacher_id=teacher_001&include_distribution=true&include_keywords=true
```

### 📋 操作日志模块

| 接口 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 创建操作日志 | POST | `/api/operation-log` | 手动创建操作日志 |
| 获取操作日志列表 | GET | `/api/operation-log` | 分页获取操作日志列表 |
| 获取操作日志详情 | GET | `/api/operation-log/:id` | 根据ID获取操作日志详情 |
| 获取操作日志统计 | GET | `/api/operation-log/statistics` | 获取操作日志统计信息 |
| 获取用户操作历史 | GET | `/api/operation-log/user/:userId/history` | 获取指定用户的操作历史 |
| 获取学校操作日志 | GET | `/api/operation-log/school/:schoolId` | 获取指定学校的操作日志 |
| 获取操作日志趋势 | GET | `/api/operation-log/trend` | 获取操作日志趋势分析 |
| 清理过期日志 | POST | `/api/operation-log/cleanup` | 清理过期的操作日志 |

**操作日志特性：**
- 🔄 **自动记录**：通过中间件自动记录所有API操作
- 📊 **统计分析**：提供操作趋势、用户活跃度等统计
- 🔍 **多维查询**：支持按用户、学校、模块、时间等维度查询
- 🛡️ **安全审计**：记录IP地址、用户代理等安全信息
- ⚡ **性能监控**：记录响应时间，监控系统性能
- 🗂️ **数据管理**：支持过期日志清理，控制存储空间

## 🗄️ 数据库设计

### 核心表结构

- **questionnaires**: 问卷表
- **responses**: 响应表
- **answers**: 答案表

### 关键字段说明

- **star_mode**: 星级模式（5星制/10星制）
- **sso_school_id**: SSO学校ID
- **sso_student_id**: SSO学生ID
- **sso_teacher_id**: SSO教师ID
- **total_average_score**: 总平均分（百分制）

## 🔧 核心特性

### 星级评分转换

- **5星制**：1星=20分, 2星=40分, 3星=60分, 4星=80分, 5星=100分
- **10星制**：1星=10分, 2星=20分, ..., 10星=100分

### 重复提交防护

基于 `家长手机号 + 问卷ID + 学生ID + 月份` 的唯一键防止重复提交。

### SSO集成

与兴教云SSO系统集成，验证学生和教师身份的有效性。

### 性能优化

- 使用原生SQL查询优化统计性能
- 建立复合索引加速查询
- 支持分页查询处理大数据量

## 🧪 测试

```bash
# 运行所有测试
npm test

# 运行特定模块测试
npm test -- test/questionnaire.test.ts
npm test -- test/response.test.ts
npm test -- test/statistics.test.ts
```

## 📚 文档

- [📡 API接口参考文档](docs/api-reference.md) - 完整的API接口清单和使用示例
- [🗂️ 问卷管理模块文档](docs/questionnaire-module.md) - 问卷创建、管理功能详解
- [📝 问卷填写模块文档](docs/response-module.md) - 响应提交、验证功能详解
- [📊 统计分析模块文档](docs/statistics-module.md) - 数据统计、分析功能详解
- [📋 操作日志模块文档](docs/operation-log-module.md) - 操作审计、日志管理功能详解

## 🛠️ 技术栈

- **框架**: Midway.js 3.x
- **语言**: TypeScript
- **数据库**: MySQL 5.7+
- **ORM**: Sequelize + sequelize-typescript
- **验证**: @midwayjs/validate
- **测试**: Jest + @midwayjs/mock

## 📦 项目结构

```
├── src/
│   ├── controller/          # 控制器
│   │   ├── questionnaire.controller.ts
│   │   ├── response.controller.ts
│   │   ├── statistics.controller.ts
│   │   └── operation-log.controller.ts
│   ├── service/            # 服务层
│   │   ├── questionnaire.service.ts
│   │   ├── response.service.ts
│   │   ├── statistics.service.ts
│   │   └── operation-log.service.ts
│   ├── entity/             # 实体定义
│   │   ├── questionnaire.entity.ts
│   │   ├── response.entity.ts
│   │   ├── answer.entity.ts
│   │   └── operation-log.entity.ts
│   ├── dto/                # 数据传输对象
│   │   ├── questionnaire.dto.ts
│   │   ├── response.dto.ts
│   │   ├── statistics.dto.ts
│   │   └── operation-log.dto.ts
│   ├── middleware/         # 中间件
│   │   └── operation-log.middleware.ts
│   ├── common/             # 公共模块
│   │   ├── APIManager.ts
│   │   └── ErrorCode.ts
│   └── config/             # 配置文件
├── test/                   # 测试文件
├── docs/                   # 文档
└── README.md
```

## 🚀 部署

### 生产环境配置

```typescript
// src/config/config.prod.ts
export default {
  koa: {
    port: 3141
  },
  sequelize: {
    dataSource: {
      default: {
        pool: {
          max: 20,
          min: 5,
          idle: 30000
        },
        logging: false
      }
    }
  }
}
```

### Docker 部署

```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3141
CMD ["npm", "start"]
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**🎉 教师评价问卷系统 - 让教育评价更科学、更高效！**
