# 操作日志模块

## 概述

操作日志模块为教师评价问卷系统提供完整的用户操作审计功能，记录所有重要的业务操作，支持操作追踪、统计分析和安全审计。

## 功能特性

- ✅ **自动日志记录**：通过中间件自动记录API调用
- ✅ **详细操作信息**：记录操作用户、时间、参数、结果等
- ✅ **多维度查询**：支持按用户、学校、模块、时间等维度查询
- ✅ **统计分析**：提供操作趋势、用户活跃度等统计
- ✅ **性能监控**：记录响应时间，监控系统性能
- ✅ **安全审计**：记录IP地址、用户代理等安全信息
- ✅ **数据清理**：支持过期日志自动清理

## 数据模型

### 操作日志实体 (OperationLog)

```typescript
{
  id: number;                      // 日志ID
  operator_user_id: string;        // 操作用户ID（SSO用户ID）
  operator_user_name: string;      // 操作用户姓名
  operator_user_role: string;      // 操作用户角色
  operator_school_id: string;      // 操作用户所属学校ID
  module: OperationModule;         // 操作模块
  operation_type: OperationType;   // 操作类型
  operation_description: string;   // 操作描述
  target_id: string;              // 操作目标ID
  target_type: string;            // 操作目标类型
  before_data: string;            // 操作前数据（JSON格式）
  after_data: string;             // 操作后数据（JSON格式）
  request_params: string;         // 请求参数（JSON格式）
  operation_status: OperationStatus; // 操作状态
  error_message: string;          // 错误信息
  ip_address: string;             // 操作IP地址
  user_agent: string;             // 用户代理信息
  request_path: string;           // 请求路径
  request_method: string;         // 请求方法
  response_time: number;          // 响应时间（毫秒）
  operation_time: Date;           // 操作时间
  remarks: string;                // 备注信息
  created_at: Date;               // 创建时间
  updated_at: Date;               // 更新时间
}
```

### 枚举定义

#### 操作模块 (OperationModule)
- `QUESTIONNAIRE`: 问卷管理
- `RESPONSE`: 问卷填写
- `STATISTICS`: 统计分析
- `SYSTEM`: 系统操作

#### 操作类型 (OperationType)
- `CREATE`: 创建
- `UPDATE`: 更新
- `DELETE`: 删除
- `QUERY`: 查询
- `SUBMIT`: 提交
- `PUBLISH`: 发布
- `CLOSE`: 关闭

#### 操作状态 (OperationStatus)
- `SUCCESS`: 成功
- `FAILED`: 失败
- `PENDING`: 进行中

## API 接口

### 1. 创建操作日志

**POST** `/api/operation-log`

**请求体：**
```json
{
  "operator_user_id": "user_001",
  "operator_user_name": "张三",
  "operator_user_role": "teacher",
  "operator_school_id": "school_001",
  "module": "questionnaire",
  "operation_type": "create",
  "operation_description": "创建问卷",
  "target_id": "1",
  "target_type": "questionnaire",
  "request_params": "{\"title\":\"测试问卷\"}",
  "operation_status": "success",
  "ip_address": "127.0.0.1",
  "user_agent": "Mozilla/5.0",
  "request_path": "/api/questionnaire",
  "request_method": "POST",
  "response_time": 150
}
```

### 2. 获取操作日志列表

**GET** `/api/operation-log`

**查询参数：**
- `operator_user_id`: 操作用户ID（可选）
- `operator_school_id`: 操作用户所属学校ID（可选）
- `module`: 操作模块（可选）
- `operation_type`: 操作类型（可选）
- `target_id`: 操作目标ID（可选）
- `target_type`: 操作目标类型（可选）
- `operation_status`: 操作状态（可选）
- `ip_address`: IP地址（可选）
- `start_time`: 开始时间（可选）
- `end_time`: 结束时间（可选）
- `page`: 页码，默认1
- `limit`: 每页数量，默认20
- `sort_by`: 排序字段，默认operation_time
- `sort_order`: 排序方向，默认DESC

### 3. 获取操作日志详情

**GET** `/api/operation-log/:id`

### 4. 获取操作日志统计

**GET** `/api/operation-log/statistics`

**查询参数：**
- `operator_school_id`: 操作用户所属学校ID（可选）
- `module`: 操作模块（可选）
- `start_time`: 开始时间（可选）
- `end_time`: 结束时间（可选）
- `granularity`: 统计粒度（day/week/month），默认day

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "获取操作日志统计成功",
  "data": {
    "total_operations": 100,
    "success_operations": 95,
    "failed_operations": 5,
    "success_rate": 95.0,
    "avg_response_time": 150.5,
    "operation_trend": [...],
    "module_distribution": [...],
    "user_activity": [...]
  }
}
```

### 5. 获取用户操作历史

**GET** `/api/operation-log/user/:userId/history`

### 6. 获取学校操作日志

**GET** `/api/operation-log/school/:schoolId`

### 7. 获取操作日志趋势

**GET** `/api/operation-log/trend`

### 8. 清理过期日志

**POST** `/api/operation-log/cleanup`

**请求体：**
```json
{
  "days_to_keep": 90
}
```

## 自动日志记录

### 操作日志中间件

系统通过 `OperationLogMiddleware` 自动记录API调用：

```typescript
@Middleware()
export class OperationLogMiddleware {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      const startTime = Date.now();
      
      try {
        await next();
        // 记录成功操作
        await this.logOperation(ctx, OperationStatus.SUCCESS, undefined, responseTime);
      } catch (error) {
        // 记录失败操作
        await this.logOperation(ctx, OperationStatus.FAILED, error.message, responseTime);
        throw error;
      }
    };
  }
}
```

### 自动记录规则

1. **包含的操作**：
   - 所有以 `/api/` 开头的接口调用
   - POST、PUT、DELETE 等修改操作
   - 重要的查询操作

2. **排除的操作**：
   - 操作日志接口本身
   - 健康检查接口
   - 静态资源请求
   - 部分只读查询接口

3. **记录的信息**：
   - 操作用户信息（从header或body中获取）
   - 请求详情（路径、方法、参数）
   - 响应信息（状态、时间、错误）
   - 环境信息（IP、用户代理）

## 使用方式

### 1. 手动记录日志

```typescript
// 在服务中注入OperationLogService
@Inject()
operationLogService: OperationLogService;

// 记录操作日志
await this.operationLogService.logOperation(
  ctx,
  'user_001',
  OperationModule.QUESTIONNAIRE,
  OperationType.CREATE,
  '创建问卷',
  {
    targetId: questionnaire.id.toString(),
    targetType: 'questionnaire',
    afterData: questionnaire,
    operatorUserName: '张三',
    operatorUserRole: 'teacher',
    operatorSchoolId: 'school_001'
  }
);
```

### 2. 通过Header传递用户信息

```http
POST /api/questionnaire
X-Operator-User-Id: user_001
X-Operator-User-Name: 张三
X-Operator-User-Role: teacher
X-Operator-School-Id: school_001
Content-Type: application/json

{
  "title": "测试问卷"
}
```

### 3. 查询操作日志

```javascript
// 获取用户操作历史
const getUserLogs = async (userId) => {
  const response = await fetch(`/api/operation-log/user/${userId}/history?page=1&limit=20`);
  return response.json();
};

// 获取学校操作统计
const getSchoolStats = async (schoolId) => {
  const response = await fetch(`/api/operation-log/statistics?operator_school_id=${schoolId}&granularity=day`);
  return response.json();
};
```

## 数据库设计

### 索引策略

```sql
-- 操作用户索引
CREATE INDEX idx_operator_user_id ON operation_logs(operator_user_id);

-- 操作时间索引
CREATE INDEX idx_operation_time ON operation_logs(operation_time);

-- 模块和类型复合索引
CREATE INDEX idx_module_type ON operation_logs(module, operation_type);

-- 目标ID和类型复合索引
CREATE INDEX idx_target_id ON operation_logs(target_id, target_type);

-- 学校ID索引
CREATE INDEX idx_operator_school_id ON operation_logs(operator_school_id);
```

### 分区策略

对于大量数据，建议按时间分区：

```sql
-- 按月分区
ALTER TABLE operation_logs PARTITION BY RANGE (YEAR(operation_time) * 100 + MONTH(operation_time)) (
  PARTITION p202401 VALUES LESS THAN (202402),
  PARTITION p202402 VALUES LESS THAN (202403),
  -- ...
);
```

## 性能优化

### 1. 异步记录

```typescript
// 异步创建日志，不阻塞主流程
setImmediate(() => {
  this.createOperationLog(logData).catch(error => {
    console.error('创建操作日志失败:', error);
  });
});
```

### 2. 批量写入

```typescript
// 批量创建日志（适用于大量操作）
const logs = [...]; // 日志数组
await this.operationLogRepository.bulkCreate(logs);
```

### 3. 数据清理

```typescript
// 定期清理过期日志
const deletedCount = await this.operationLogService.cleanupExpiredLogs(90); // 保留90天
```

## 安全考虑

### 1. 敏感信息过滤

```typescript
// 移除敏感信息
const sanitizeData = (data: any) => {
  const sanitized = { ...data };
  delete sanitized.password;
  delete sanitized.token;
  delete sanitized.secret;
  return sanitized;
};
```

### 2. 访问控制

- 只有管理员可以查看所有日志
- 普通用户只能查看自己的操作日志
- 学校管理员只能查看本校的操作日志

### 3. 数据保护

- 日志数据加密存储
- 定期备份重要日志
- 设置日志保留期限

## 监控告警

### 1. 异常操作监控

```typescript
// 监控失败操作
if (failedOperations > threshold) {
  // 发送告警
  await sendAlert('操作失败率过高');
}
```

### 2. 性能监控

```typescript
// 监控响应时间
if (avgResponseTime > threshold) {
  // 发送告警
  await sendAlert('系统响应时间过长');
}
```

### 3. 安全监控

```typescript
// 监控异常IP
if (suspiciousIP) {
  // 发送安全告警
  await sendSecurityAlert('检测到异常IP访问');
}
```

## 扩展功能

### 1. 操作回滚

基于操作日志实现数据回滚：

```typescript
async rollbackOperation(logId: number) {
  const log = await this.getOperationLogById(logId);
  if (log.before_data) {
    // 根据before_data恢复数据
    await this.restoreData(log.target_type, log.target_id, JSON.parse(log.before_data));
  }
}
```

### 2. 操作重放

重放历史操作：

```typescript
async replayOperation(logId: number) {
  const log = await this.getOperationLogById(logId);
  // 重新执行操作
  await this.executeOperation(log.operation_type, JSON.parse(log.request_params));
}
```

### 3. 智能分析

基于操作日志进行智能分析：

```typescript
async analyzeUserBehavior(userId: string) {
  const logs = await this.getUserOperationLogs(userId);
  // 分析用户行为模式
  return this.behaviorAnalyzer.analyze(logs);
}
```

---

**📝 操作日志模块为系统提供了完整的审计追踪能力，确保所有操作都有据可查！**
