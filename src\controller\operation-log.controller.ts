import {
  Controller,
  Get,
  Post,
  Inject,
  Query,
  Param,
  Body,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { OperationLogService } from '../service/operation-log.service';
import {
  CreateOperationLogDTO,
  QueryOperationLogDTO,
  OperationLogStatisticsDTO,
} from '../dto/operation-log.dto';
import { ErrorCode } from '../common/ErrorCode';

@Controller('/api/operation-log')
export class OperationLogController {
  @Inject()
  ctx: Context;

  @Inject()
  operationLogService: OperationLogService;

  /**
   * 创建操作日志
   */
  @Post('/')
  @Validate()
  async createOperationLog(@Body() createDto: CreateOperationLogDTO) {
    try {
      this.ctx.logger.info('创建操作日志请求', {
        operator_user_id: createDto.operator_user_id,
        module: createDto.module,
        operation_type: createDto.operation_type,
        operation_description: createDto.operation_description,
      });

      const operationLog = await this.operationLogService.createOperationLog(
        createDto
      );

      return {
        errCode: ErrorCode.OK,
        msg: '创建操作日志成功',
        data: {
          id: operationLog.id,
          operation_time: operationLog.operation_time,
        },
      };
    } catch (error) {
      this.ctx.logger.error('创建操作日志失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '创建操作日志失败',
        data: null,
      };
    }
  }

  /**
   * 获取操作日志列表
   */
  @Get('/')
  @Validate()
  async getOperationLogList(@Query() queryDto: QueryOperationLogDTO) {
    try {
      this.ctx.logger.info('获取操作日志列表请求', {
        operator_user_id: queryDto.operator_user_id,
        operator_school_id: queryDto.operator_school_id,
        module: queryDto.module,
        operation_type: queryDto.operation_type,
        page: queryDto.page,
        limit: queryDto.limit,
      });

      const result = await this.operationLogService.getOperationLogList(
        queryDto
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取操作日志列表成功',
        data: result,
      };
    } catch (error) {
      this.ctx.logger.error('获取操作日志列表失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取操作日志列表失败',
        data: null,
      };
    }
  }

  /**
   * 根据ID获取操作日志详情
   */
  @Get('/:id')
  async getOperationLogById(@Param('id') id: number) {
    try {
      this.ctx.logger.info('获取操作日志详情请求', { id });

      const operationLog = await this.operationLogService.getOperationLogById(
        id
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取操作日志详情成功',
        data: operationLog,
      };
    } catch (error) {
      this.ctx.logger.error('获取操作日志详情失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取操作日志详情失败',
        data: null,
      };
    }
  }

  /**
   * 获取操作日志统计
   */
  @Get('/statistics')
  @Validate()
  async getOperationLogStatistics(
    @Query() queryDto: OperationLogStatisticsDTO
  ) {
    try {
      this.ctx.logger.info('获取操作日志统计请求', {
        operator_school_id: queryDto.operator_school_id,
        module: queryDto.module,
        start_time: queryDto.start_time,
        end_time: queryDto.end_time,
        granularity: queryDto.granularity,
      });

      const statistics =
        await this.operationLogService.getOperationLogStatistics(queryDto);

      return {
        errCode: ErrorCode.OK,
        msg: '获取操作日志统计成功',
        data: statistics,
      };
    } catch (error) {
      this.ctx.logger.error('获取操作日志统计失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取操作日志统计失败',
        data: null,
      };
    }
  }

  /**
   * 获取用户操作历史
   */
  @Get('/user/:userId/history')
  async getUserOperationHistory(
    @Param('userId') userId: string,
    @Query()
    query: {
      module?: string;
      operation_type?: string;
      start_time?: Date;
      end_time?: Date;
      page?: number;
      limit?: number;
    }
  ) {
    try {
      this.ctx.logger.info('获取用户操作历史请求', {
        userId,
        module: query.module,
        operation_type: query.operation_type,
      });

      const queryDto: QueryOperationLogDTO = {
        operator_user_id: userId,
        module: query.module as any,
        operation_type: query.operation_type as any,
        start_time: query.start_time,
        end_time: query.end_time,
        page: query.page || 1,
        limit: query.limit || 20,
      };

      const result = await this.operationLogService.getOperationLogList(
        queryDto
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取用户操作历史成功',
        data: result,
      };
    } catch (error) {
      this.ctx.logger.error('获取用户操作历史失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取用户操作历史失败',
        data: null,
      };
    }
  }

  /**
   * 获取学校操作日志
   */
  @Get('/school/:schoolId')
  async getSchoolOperationLogs(
    @Param('schoolId') schoolId: string,
    @Query()
    query: {
      module?: string;
      operation_type?: string;
      start_time?: Date;
      end_time?: Date;
      page?: number;
      limit?: number;
    }
  ) {
    try {
      this.ctx.logger.info('获取学校操作日志请求', {
        schoolId,
        module: query.module,
        operation_type: query.operation_type,
      });

      const queryDto: QueryOperationLogDTO = {
        operator_school_id: schoolId,
        module: query.module as any,
        operation_type: query.operation_type as any,
        start_time: query.start_time,
        end_time: query.end_time,
        page: query.page || 1,
        limit: query.limit || 20,
      };

      const result = await this.operationLogService.getOperationLogList(
        queryDto
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取学校操作日志成功',
        data: result,
      };
    } catch (error) {
      this.ctx.logger.error('获取学校操作日志失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取学校操作日志失败',
        data: null,
      };
    }
  }

  /**
   * 获取操作日志趋势
   */
  @Get('/trend')
  @Validate()
  async getOperationLogTrend(@Query() queryDto: OperationLogStatisticsDTO) {
    try {
      this.ctx.logger.info('获取操作日志趋势请求', {
        operator_school_id: queryDto.operator_school_id,
        module: queryDto.module,
        granularity: queryDto.granularity,
      });

      const statistics =
        await this.operationLogService.getOperationLogStatistics(queryDto);

      return {
        errCode: ErrorCode.OK,
        msg: '获取操作日志趋势成功',
        data: {
          operation_trend: statistics.operation_trend,
          module_distribution: statistics.module_distribution,
          user_activity: statistics.user_activity,
        },
      };
    } catch (error) {
      this.ctx.logger.error('获取操作日志趋势失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取操作日志趋势失败',
        data: null,
      };
    }
  }

  /**
   * 清理过期日志
   */
  @Post('/cleanup')
  async cleanupExpiredLogs(@Body() body: { days_to_keep?: number }) {
    try {
      const daysToKeep = body.days_to_keep || 90;

      this.ctx.logger.info('清理过期日志请求', { daysToKeep });

      const deletedCount = await this.operationLogService.cleanupExpiredLogs(
        daysToKeep
      );

      return {
        errCode: ErrorCode.OK,
        msg: '清理过期日志成功',
        data: {
          deleted_count: deletedCount,
          days_to_keep: daysToKeep,
        },
      };
    } catch (error) {
      this.ctx.logger.error('清理过期日志失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '清理过期日志失败',
        data: null,
      };
    }
  }
}
