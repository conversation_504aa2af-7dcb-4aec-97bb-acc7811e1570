import {
  Controller,
  Post,
  Get,
  Put,
  Del,
  Inject,
  Body,
  Param,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { QuestionnaireService } from '../service/questionnaire.service';
import {
  CreateQuestionnaireDTO,
  UpdateQuestionnaireStatusDTO,
  QueryQuestionnaireDTO,
} from '../dto/questionnaire.dto';
import { ErrorCode } from '../common/ErrorCode';

@Controller('/api/questionnaire')
export class QuestionnaireController {
  @Inject()
  ctx: Context;

  @Inject()
  questionnaireService: QuestionnaireService;

  /**
   * 创建问卷
   */
  @Post('/')
  @Validate()
  async createQuestionnaire(@Body() createDto: CreateQuestionnaireDTO) {
    try {
      // 从JWT token中获取用户信息（假设已经通过JWT中间件验证）
      const userInfo = this.ctx.state.user || {};
      const creatorUserId = userInfo.userId || userInfo.id;
      const creatorUserName = userInfo.userName || userInfo.name;

      if (!creatorUserId) {
        return {
          errCode: ErrorCode.AUTH_ERROR,
          msg: '用户未登录或token无效',
          data: null,
        };
      }

      const questionnaire = await this.questionnaireService.createQuestionnaire(
        createDto,
        creatorUserId,
        creatorUserName
      );

      return {
        errCode: ErrorCode.OK,
        msg: '问卷创建成功',
        data: questionnaire,
      };
    } catch (error) {
      this.ctx.logger.error('创建问卷失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '创建问卷失败',
        data: null,
      };
    }
  }

  /**
   * 获取问卷列表
   */
  @Get('/')
  @Validate()
  async getQuestionnaireList(@Query() queryDto: QueryQuestionnaireDTO) {
    try {
      const result = await this.questionnaireService.getQuestionnaireList(
        queryDto
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取问卷列表成功',
        data: result,
      };
    } catch (error) {
      this.ctx.logger.error('获取问卷列表失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取问卷列表失败',
        data: null,
      };
    }
  }

  /**
   * 根据ID获取问卷详情
   */
  @Get('/:id')
  async getQuestionnaireById(@Param('id') id: number) {
    try {
      const questionnaire =
        await this.questionnaireService.getQuestionnaireById(id);

      return {
        errCode: ErrorCode.OK,
        msg: '获取问卷详情成功',
        data: questionnaire,
      };
    } catch (error) {
      this.ctx.logger.error('获取问卷详情失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取问卷详情失败',
        data: null,
      };
    }
  }

  /**
   * 更新问卷状态
   */
  @Put('/:id/status')
  @Validate()
  async updateQuestionnaireStatus(
    @Param('id') id: number,
    @Body() updateDto: UpdateQuestionnaireStatusDTO
  ) {
    try {
      // 从JWT token中获取用户信息
      const userInfo = this.ctx.state.user || {};
      const operatorUserId = userInfo.userId || userInfo.id;

      if (!operatorUserId) {
        return {
          errCode: ErrorCode.AUTH_ERROR,
          msg: '用户未登录或token无效',
          data: null,
        };
      }

      const questionnaire =
        await this.questionnaireService.updateQuestionnaireStatus(
          id,
          updateDto,
          operatorUserId
        );

      return {
        errCode: ErrorCode.OK,
        msg: '问卷状态更新成功',
        data: questionnaire,
      };
    } catch (error) {
      this.ctx.logger.error('更新问卷状态失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '更新问卷状态失败',
        data: null,
      };
    }
  }

  /**
   * 删除问卷（仅限草稿状态）
   */
  @Del('/:id')
  async deleteQuestionnaire(@Param('id') id: number) {
    try {
      // 从JWT token中获取用户信息
      const userInfo = this.ctx.state.user || {};
      const operatorUserId = userInfo.userId || userInfo.id;

      if (!operatorUserId) {
        return {
          errCode: ErrorCode.AUTH_ERROR,
          msg: '用户未登录或token无效',
          data: null,
        };
      }

      await this.questionnaireService.deleteQuestionnaire(id, operatorUserId);

      return {
        errCode: ErrorCode.OK,
        msg: '问卷删除成功',
        data: null,
      };
    } catch (error) {
      this.ctx.logger.error('删除问卷失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '删除问卷失败',
        data: null,
      };
    }
  }

  /**
   * 获取问卷统计信息
   */
  @Get('/:id/statistics')
  async getQuestionnaireStatistics(@Param('id') id: number) {
    try {
      // 验证问卷是否存在
      await this.questionnaireService.getQuestionnaireById(id);

      // TODO: 实现统计逻辑
      const statistics = {
        questionnaire_id: id,
        total_responses: 0,
        completed_responses: 0,
        completion_rate: 0,
        average_rating: 0,
      };

      return {
        errCode: ErrorCode.OK,
        msg: '获取问卷统计信息成功',
        data: statistics,
      };
    } catch (error) {
      this.ctx.logger.error('获取问卷统计信息失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取问卷统计信息失败',
        data: null,
      };
    }
  }
}
