import {
  Controller,
  Post,
  Get,
  Inject,
  Body,
  Param,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { ResponseService } from '../service/response.service';
import { SubmitResponseDTO, QueryResponseDTO } from '../dto/response.dto';
import { ErrorCode } from '../common/ErrorCode';

@Controller('/api/response')
export class ResponseController {
  @Inject()
  ctx: Context;

  @Inject()
  responseService: ResponseService;

  /**
   * 提交问卷响应
   */
  @Post('/')
  @Validate()
  async submitResponse(@Body() submitDto: SubmitResponseDTO) {
    try {
      // 记录提交IP
      const clientIP =
        this.ctx.request.ip ||
        this.ctx.request.headers['x-forwarded-for'] ||
        this.ctx.request.headers['x-real-ip'] ||
        'unknown';

      this.ctx.logger.info('问卷响应提交请求', {
        questionnaire_id: submitDto.questionnaire_id,
        parent_phone: submitDto.parent_phone,
        student_id: submitDto.sso_student_id,
        month: submitDto.month,
        teacher_count: submitDto.teacher_evaluations.length,
        client_ip: clientIP,
      });

      const response = await this.responseService.submitResponse(submitDto);

      this.ctx.logger.info('问卷响应提交成功', {
        response_id: response.id,
        questionnaire_id: submitDto.questionnaire_id,
        parent_phone: submitDto.parent_phone,
      });

      return {
        errCode: ErrorCode.OK,
        msg: '问卷提交成功',
        data: {
          response_id: response.id,
          questionnaire_id: response.questionnaire_id,
          submission_time: response.created_at,
          total_average_score: response.total_average_score,
          teacher_count: response.teacher_count,
        },
      };
    } catch (error) {
      this.ctx.logger.error('提交问卷响应失败', error, {
        questionnaire_id: submitDto.questionnaire_id,
        parent_phone: submitDto.parent_phone,
        student_id: submitDto.sso_student_id,
      });

      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '提交问卷失败',
        data: null,
      };
    }
  }

  /**
   * 获取响应列表
   */
  @Get('/')
  @Validate()
  async getResponseList(@Query() queryDto: QueryResponseDTO) {
    try {
      const result = await this.responseService.getResponseList(queryDto);

      return {
        errCode: ErrorCode.OK,
        msg: '获取响应列表成功',
        data: result,
      };
    } catch (error) {
      this.ctx.logger.error('获取响应列表失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取响应列表失败',
        data: null,
      };
    }
  }

  /**
   * 根据ID获取响应详情
   */
  @Get('/:id')
  async getResponseById(@Param('id') id: number) {
    try {
      const response = await this.responseService.getResponseById(id);

      return {
        errCode: ErrorCode.OK,
        msg: '获取响应详情成功',
        data: response,
      };
    } catch (error) {
      this.ctx.logger.error('获取响应详情失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取响应详情失败',
        data: null,
      };
    }
  }

  /**
   * 检查是否已提交响应
   */
  @Get('/check')
  async checkResponseExists(
    @Query()
    query: {
      parent_phone: string;
      questionnaire_id: number;
      sso_student_id: string;
      month: string;
    }
  ) {
    try {
      const { parent_phone, questionnaire_id, sso_student_id, month } = query;

      if (!parent_phone || !questionnaire_id || !sso_student_id || !month) {
        return {
          errCode: ErrorCode.PARAM_ERROR,
          msg: '缺少必要参数',
          data: null,
        };
      }

      const exists = await this.responseService.checkResponseExists(
        parent_phone,
        questionnaire_id,
        sso_student_id,
        month
      );

      return {
        errCode: ErrorCode.OK,
        msg: '检查完成',
        data: {
          exists,
          message: exists ? '该家长已为此学生在本月提交过问卷' : '可以提交问卷',
        },
      };
    } catch (error) {
      this.ctx.logger.error('检查响应是否存在失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '检查失败',
        data: null,
      };
    }
  }

  /**
   * 获取问卷统计信息
   */
  @Get('/statistics/:questionnaireId')
  async getQuestionnaireStatistics(
    @Param('questionnaireId') questionnaireId: number
  ) {
    try {
      const statistics = await this.responseService.getQuestionnaireStatistics(
        questionnaireId
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取统计信息成功',
        data: statistics,
      };
    } catch (error) {
      this.ctx.logger.error('获取问卷统计信息失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取统计信息失败',
        data: null,
      };
    }
  }

  /**
   * 获取星级评分转换说明
   */
  @Get('/rating-info')
  async getRatingInfo() {
    try {
      // 这里可以获取问卷的星级模式，返回评分说明
      return {
        errCode: ErrorCode.OK,
        msg: '获取评分信息成功',
        data: {
          five_star_mode: {
            description: '5星制评分',
            conversion: '1星=20分, 2星=40分, 3星=60分, 4星=80分, 5星=100分',
            ratings: [
              { star: 1, score: 20, description: '很不满意' },
              { star: 2, score: 40, description: '不满意' },
              { star: 3, score: 60, description: '一般' },
              { star: 4, score: 80, description: '满意' },
              { star: 5, score: 100, description: '很满意' },
            ],
          },
          ten_star_mode: {
            description: '10星制评分',
            conversion: '1星=10分, 2星=20分, ..., 10星=100分',
            ratings: Array.from({ length: 10 }, (_, i) => ({
              star: i + 1,
              score: (i + 1) * 10,
              description:
                i < 3 ? '不满意' : i < 6 ? '一般' : i < 8 ? '满意' : '很满意',
            })),
          },
        },
      };
    } catch (error) {
      this.ctx.logger.error('获取评分信息失败', error);
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取评分信息失败',
        data: null,
      };
    }
  }
}
