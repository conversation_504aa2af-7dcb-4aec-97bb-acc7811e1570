import { Controller, Get, Inject, Query, Param } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { StatisticsService } from '../service/statistics.service';
import {
  SchoolStatisticsQueryDTO,
  TeacherStatisticsQueryDTO,
  TeacherRankingQueryDTO,
  TrendAnalysisQueryDTO,
} from '../dto/statistics.dto';
import { ErrorCode } from '../common/ErrorCode';

@Controller('/api/statistics')
export class StatisticsController {
  @Inject()
  ctx: Context;

  @Inject()
  statisticsService: StatisticsService;

  /**
   * 获取学校维度统计
   */
  @Get('/school')
  @Validate()
  async getSchoolStatistics(@Query() queryDto: SchoolStatisticsQueryDTO) {
    try {
      this.ctx.logger.info('获取学校统计数据请求', {
        sso_school_id: queryDto.sso_school_id,
        month: queryDto.month,
        start_month: queryDto.start_month,
        end_month: queryDto.end_month,
      });

      const statistics = await this.statisticsService.getSchoolStatistics(
        queryDto
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取学校统计数据成功',
        data: statistics,
      };
    } catch (error) {
      this.ctx.logger.error('获取学校统计数据失败', error, {
        sso_school_id: queryDto.sso_school_id,
      });

      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取学校统计数据失败',
        data: null,
      };
    }
  }

  /**
   * 获取教师维度统计
   */
  @Get('/teacher')
  @Validate()
  async getTeacherStatistics(@Query() queryDto: TeacherStatisticsQueryDTO) {
    try {
      this.ctx.logger.info('获取教师统计数据请求', {
        sso_teacher_id: queryDto.sso_teacher_id,
        sso_school_id: queryDto.sso_school_id,
        month: queryDto.month,
      });

      const statistics = await this.statisticsService.getTeacherStatistics(
        queryDto
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取教师统计数据成功',
        data: statistics,
      };
    } catch (error) {
      this.ctx.logger.error('获取教师统计数据失败', error, {
        sso_teacher_id: queryDto.sso_teacher_id,
      });

      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取教师统计数据失败',
        data: null,
      };
    }
  }

  /**
   * 获取教师排名
   */
  @Get('/teacher-ranking')
  @Validate()
  async getTeacherRanking(@Query() queryDto: TeacherRankingQueryDTO) {
    try {
      this.ctx.logger.info('获取教师排名请求', {
        sso_school_id: queryDto.sso_school_id,
        month: queryDto.month,
        subject: queryDto.subject,
        department: queryDto.department,
        sort_by: queryDto.sort_by,
      });

      const ranking = await this.statisticsService.getTeacherRanking(queryDto);

      return {
        errCode: ErrorCode.OK,
        msg: '获取教师排名成功',
        data: ranking,
      };
    } catch (error) {
      this.ctx.logger.error('获取教师排名失败', error, {
        sso_school_id: queryDto.sso_school_id,
      });

      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取教师排名失败',
        data: null,
      };
    }
  }

  /**
   * 获取趋势分析数据
   */
  @Get('/trend')
  @Validate()
  async getTrendAnalysis(@Query() queryDto: TrendAnalysisQueryDTO) {
    try {
      this.ctx.logger.info('获取趋势分析数据请求', {
        sso_school_id: queryDto.sso_school_id,
        start_month: queryDto.start_month,
        end_month: queryDto.end_month,
        analysis_type: queryDto.analysis_type,
      });

      const trendData = await this.statisticsService.getTrendAnalysis(queryDto);

      return {
        errCode: ErrorCode.OK,
        msg: '获取趋势分析数据成功',
        data: trendData,
      };
    } catch (error) {
      this.ctx.logger.error('获取趋势分析数据失败', error, {
        sso_school_id: queryDto.sso_school_id,
      });

      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取趋势分析数据失败',
        data: null,
      };
    }
  }

  /**
   * 获取教师评分分布
   */
  @Get('/teacher/:teacherId/distribution')
  async getTeacherScoreDistribution(
    @Param('teacherId') teacherId: string,
    @Query() query: { sso_school_id?: string; month?: string }
  ) {
    try {
      const { sso_school_id, month } = query;

      this.ctx.logger.info('获取教师评分分布请求', {
        sso_teacher_id: teacherId,
        sso_school_id,
        month,
      });

      const distribution = await this.statisticsService.getScoreDistribution(
        teacherId,
        sso_school_id,
        month
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取教师评分分布成功',
        data: distribution,
      };
    } catch (error) {
      this.ctx.logger.error('获取教师评分分布失败', error, {
        sso_teacher_id: teacherId,
      });

      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取教师评分分布失败',
        data: null,
      };
    }
  }

  /**
   * 获取教师关键词云
   */
  @Get('/teacher/:teacherId/keywords')
  async getTeacherKeywords(
    @Param('teacherId') teacherId: string,
    @Query() query: { sso_school_id?: string; month?: string }
  ) {
    try {
      const { sso_school_id, month } = query;

      this.ctx.logger.info('获取教师关键词云请求', {
        sso_teacher_id: teacherId,
        sso_school_id,
        month,
      });

      const keywords = await this.statisticsService.getKeywordCloud(
        teacherId,
        sso_school_id,
        month
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取教师关键词云成功',
        data: keywords,
      };
    } catch (error) {
      this.ctx.logger.error('获取教师关键词云失败', error, {
        sso_teacher_id: teacherId,
      });

      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取教师关键词云失败',
        data: null,
      };
    }
  }

  /**
   * 获取学校响应趋势
   */
  @Get('/school/:schoolId/trend')
  async getSchoolTrend(
    @Param('schoolId') schoolId: string,
    @Query() query: { start_month: string; end_month: string }
  ) {
    try {
      const { start_month, end_month } = query;

      if (!start_month || !end_month) {
        return {
          errCode: ErrorCode.PARAM_ERROR,
          msg: '开始月份和结束月份是必填参数',
          data: null,
        };
      }

      this.ctx.logger.info('获取学校响应趋势请求', {
        sso_school_id: schoolId,
        start_month,
        end_month,
      });

      const trendData = await this.statisticsService.getResponseTrend(
        schoolId,
        start_month,
        end_month
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取学校响应趋势成功',
        data: trendData,
      };
    } catch (error) {
      this.ctx.logger.error('获取学校响应趋势失败', error, {
        sso_school_id: schoolId,
      });

      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取学校响应趋势失败',
        data: null,
      };
    }
  }

  /**
   * 获取教师评价趋势
   */
  @Get('/teacher/:teacherId/trend')
  async getTeacherTrend(
    @Param('teacherId') teacherId: string,
    @Query()
    query: {
      sso_school_id?: string;
      start_month?: string;
      end_month?: string;
    }
  ) {
    try {
      const { sso_school_id, start_month, end_month } = query;

      this.ctx.logger.info('获取教师评价趋势请求', {
        sso_teacher_id: teacherId,
        sso_school_id,
        start_month,
        end_month,
      });

      const trendData = await this.statisticsService.getTeacherTrend(
        teacherId,
        sso_school_id,
        start_month,
        end_month
      );

      return {
        errCode: ErrorCode.OK,
        msg: '获取教师评价趋势成功',
        data: trendData,
      };
    } catch (error) {
      this.ctx.logger.error('获取教师评价趋势失败', error, {
        sso_teacher_id: teacherId,
      });

      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '获取教师评价趋势失败',
        data: null,
      };
    }
  }
}
