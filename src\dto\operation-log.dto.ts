import { Rule, RuleType } from '@midwayjs/validate';
import {
  OperationType,
  OperationModule,
  OperationStatus,
} from '../entity/operation-log.entity';

/**
 * 创建操作日志DTO
 */
export class CreateOperationLogDTO {
  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': '操作用户ID不能为空',
      'string.max': '操作用户ID不能超过50个字符',
      'any.required': '操作用户ID是必填项',
    })
  )
  operator_user_id: string;

  @Rule(
    RuleType.string().optional().max(100).messages({
      'string.max': '操作用户姓名不能超过100个字符',
    })
  )
  operator_user_name?: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': '操作用户角色不能超过50个字符',
    })
  )
  operator_user_role?: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': '操作用户所属学校ID不能超过50个字符',
    })
  )
  operator_school_id?: string;

  @Rule(
    RuleType.string()
      .valid(...Object.values(OperationModule))
      .required()
      .messages({
        'any.only': '操作模块必须是有效值',
        'any.required': '操作模块是必填项',
      })
  )
  module: OperationModule;

  @Rule(
    RuleType.string()
      .valid(...Object.values(OperationType))
      .required()
      .messages({
        'any.only': '操作类型必须是有效值',
        'any.required': '操作类型是必填项',
      })
  )
  operation_type: OperationType;

  @Rule(
    RuleType.string().required().max(200).messages({
      'string.empty': '操作描述不能为空',
      'string.max': '操作描述不能超过200个字符',
      'any.required': '操作描述是必填项',
    })
  )
  operation_description: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': '操作目标ID不能超过50个字符',
    })
  )
  target_id?: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': '操作目标类型不能超过50个字符',
    })
  )
  target_type?: string;

  @Rule(
    RuleType.string().optional().messages({
      'string.base': '操作前数据必须是字符串',
    })
  )
  before_data?: string;

  @Rule(
    RuleType.string().optional().messages({
      'string.base': '操作后数据必须是字符串',
    })
  )
  after_data?: string;

  @Rule(
    RuleType.string().optional().messages({
      'string.base': '请求参数必须是字符串',
    })
  )
  request_params?: string;

  @Rule(
    RuleType.string()
      .valid(...Object.values(OperationStatus))
      .optional()
      .messages({
        'any.only': '操作状态必须是有效值',
      })
  )
  operation_status?: OperationStatus;

  @Rule(
    RuleType.string().optional().max(500).messages({
      'string.max': '错误信息不能超过500个字符',
    })
  )
  error_message?: string;

  @Rule(
    RuleType.string().optional().max(45).messages({
      'string.max': 'IP地址不能超过45个字符',
    })
  )
  ip_address?: string;

  @Rule(
    RuleType.string().optional().max(500).messages({
      'string.max': '用户代理信息不能超过500个字符',
    })
  )
  user_agent?: string;

  @Rule(
    RuleType.string().optional().max(200).messages({
      'string.max': '请求路径不能超过200个字符',
    })
  )
  request_path?: string;

  @Rule(
    RuleType.string().optional().max(10).messages({
      'string.max': '请求方法不能超过10个字符',
    })
  )
  request_method?: string;

  @Rule(
    RuleType.number().integer().optional().min(0).messages({
      'number.min': '响应时间不能小于0',
      'number.integer': '响应时间必须是整数',
    })
  )
  response_time?: number;

  @Rule(
    RuleType.date().optional().messages({
      'date.base': '操作时间必须是有效日期',
    })
  )
  operation_time?: Date;

  @Rule(
    RuleType.string().optional().messages({
      'string.base': '备注信息必须是字符串',
    })
  )
  remarks?: string;
}

/**
 * 查询操作日志DTO
 */
export class QueryOperationLogDTO {
  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': '操作用户ID不能超过50个字符',
    })
  )
  operator_user_id?: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': '操作用户所属学校ID不能超过50个字符',
    })
  )
  operator_school_id?: string;

  @Rule(
    RuleType.string()
      .valid(...Object.values(OperationModule))
      .optional()
      .messages({
        'any.only': '操作模块必须是有效值',
      })
  )
  module?: OperationModule;

  @Rule(
    RuleType.string()
      .valid(...Object.values(OperationType))
      .optional()
      .messages({
        'any.only': '操作类型必须是有效值',
      })
  )
  operation_type?: OperationType;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': '操作目标ID不能超过50个字符',
    })
  )
  target_id?: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': '操作目标类型不能超过50个字符',
    })
  )
  target_type?: string;

  @Rule(
    RuleType.string()
      .valid(...Object.values(OperationStatus))
      .optional()
      .messages({
        'any.only': '操作状态必须是有效值',
      })
  )
  operation_status?: OperationStatus;

  @Rule(
    RuleType.string().optional().max(45).messages({
      'string.max': 'IP地址不能超过45个字符',
    })
  )
  ip_address?: string;

  @Rule(
    RuleType.date().optional().messages({
      'date.base': '开始时间必须是有效日期',
    })
  )
  start_time?: Date;

  @Rule(
    RuleType.date().optional().messages({
      'date.base': '结束时间必须是有效日期',
    })
  )
  end_time?: Date;

  @Rule(
    RuleType.number().integer().min(1).default(1).messages({
      'number.min': '页码不能小于1',
      'number.integer': '页码必须是整数',
    })
  )
  page?: number;

  @Rule(
    RuleType.number().integer().min(1).max(100).default(20).messages({
      'number.min': '每页数量不能小于1',
      'number.max': '每页数量不能超过100',
      'number.integer': '每页数量必须是整数',
    })
  )
  limit?: number;

  @Rule(
    RuleType.string()
      .valid('operation_time', 'created_at', 'response_time')
      .default('operation_time')
      .messages({
        'any.only': '排序字段只能是operation_time、created_at或response_time',
      })
  )
  sort_by?: string;

  @Rule(
    RuleType.string().valid('ASC', 'DESC').default('DESC').messages({
      'any.only': '排序方向只能是ASC或DESC',
    })
  )
  sort_order?: string;
}

/**
 * 操作日志统计DTO
 */
export class OperationLogStatisticsDTO {
  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': '操作用户所属学校ID不能超过50个字符',
    })
  )
  operator_school_id?: string;

  @Rule(
    RuleType.string()
      .valid(...Object.values(OperationModule))
      .optional()
      .messages({
        'any.only': '操作模块必须是有效值',
      })
  )
  module?: OperationModule;

  @Rule(
    RuleType.date().optional().messages({
      'date.base': '开始时间必须是有效日期',
    })
  )
  start_time?: Date;

  @Rule(
    RuleType.date().optional().messages({
      'date.base': '结束时间必须是有效日期',
    })
  )
  end_time?: Date;

  @Rule(
    RuleType.string().valid('day', 'week', 'month').default('day').messages({
      'any.only': '统计粒度只能是day、week或month',
    })
  )
  granularity?: string;
}
