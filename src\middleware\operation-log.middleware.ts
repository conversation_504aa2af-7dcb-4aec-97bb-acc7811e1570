import { Middleware } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';
import { OperationLogService } from '../service/operation-log.service';
import {
  OperationModule,
  OperationType,
  OperationStatus,
} from '../entity/operation-log.entity';

@Middleware()
export class OperationLogMiddleware {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      const startTime = Date.now();
      let operationStatus = OperationStatus.SUCCESS;
      let errorMessage: string | undefined;

      try {
        await next();
      } catch (error) {
        operationStatus = OperationStatus.FAILED;
        errorMessage = error.message;
        throw error;
      } finally {
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        // 只记录特定的API操作
        if (this.shouldLogOperation(ctx)) {
          await this.logOperation(
            ctx,
            operationStatus,
            errorMessage,
            responseTime
          );
        }
      }
    };
  }

  /**
   * 判断是否需要记录操作日志
   * @param ctx Koa上下文
   * @returns 是否需要记录
   */
  private shouldLogOperation(ctx: Context): boolean {
    const path = ctx.request.path;
    const method = ctx.request.method;

    // 排除不需要记录的路径
    const excludePaths = [
      '/api/operation-log', // 操作日志接口本身
      '/health',
      '/favicon.ico',
      '/api/response/rating-info', // 获取评分信息（只读操作）
    ];

    // 排除GET请求的统计查询接口（只记录重要的业务操作）
    const excludeGetPaths = [
      '/api/statistics/',
      '/api/response/check',
      '/api/response/statistics',
    ];

    if (excludePaths.some(excludePath => path.startsWith(excludePath))) {
      return false;
    }

    if (
      method === 'GET' &&
      excludeGetPaths.some(excludePath => path.startsWith(excludePath))
    ) {
      return false;
    }

    // 只记录API接口调用
    return path.startsWith('/api/');
  }

  /**
   * 记录操作日志
   * @param ctx Koa上下文
   * @param operationStatus 操作状态
   * @param errorMessage 错误信息
   * @param responseTime 响应时间
   */
  private async logOperation(
    ctx: Context,
    operationStatus: OperationStatus,
    errorMessage: string | undefined,
    responseTime: number
  ): Promise<void> {
    try {
      const operationLogService = await ctx.requestContext.getAsync(
        OperationLogService
      );

      // 从请求中获取操作用户信息（这里假设从header或body中获取）
      const operatorUserId = this.getOperatorUserId(ctx);

      if (!operatorUserId) {
        return; // 没有操作用户ID则不记录
      }

      const { module, operationType, description, targetId, targetType } =
        this.parseOperationInfo(ctx);

      await operationLogService.logOperation(
        ctx,
        operatorUserId,
        module,
        operationType,
        description,
        {
          targetId,
          targetType,
          requestParams: this.getRequestParams(ctx),
          operationStatus,
          errorMessage,
          responseTime,
          operatorUserName: this.getOperatorUserName(ctx),
          operatorUserRole: this.getOperatorUserRole(ctx),
          operatorSchoolId: this.getOperatorSchoolId(ctx),
        }
      );
    } catch (error) {
      // 记录日志失败不应该影响主业务
      console.error('操作日志中间件记录失败:', error);
    }
  }

  /**
   * 获取操作用户ID
   * @param ctx Koa上下文
   * @returns 操作用户ID
   */
  private getOperatorUserId(ctx: Context): string | undefined {
    // 优先从header中获取
    let operatorUserId = ctx.request.headers['x-operator-user-id'] as string;

    // 如果header中没有，尝试从body中获取
    if (
      !operatorUserId &&
      ctx.request.body &&
      typeof ctx.request.body === 'object'
    ) {
      const body = ctx.request.body as any;
      operatorUserId =
        body.operator_user_id || body.sso_student_id || body.sso_teacher_id;
    }

    // 如果还没有，尝试从query中获取
    if (!operatorUserId) {
      operatorUserId =
        (ctx.request.query.operator_user_id as string) ||
        (ctx.request.query.sso_student_id as string) ||
        (ctx.request.query.sso_teacher_id as string);
    }

    return operatorUserId;
  }

  /**
   * 获取操作用户姓名
   * @param ctx Koa上下文
   * @returns 操作用户姓名
   */
  private getOperatorUserName(ctx: Context): string | undefined {
    const body = ctx.request.body as any;
    return (
      (ctx.request.headers['x-operator-user-name'] as string) ||
      body?.operator_user_name ||
      body?.parent_name
    );
  }

  /**
   * 获取操作用户角色
   * @param ctx Koa上下文
   * @returns 操作用户角色
   */
  private getOperatorUserRole(ctx: Context): string | undefined {
    const body = ctx.request.body as any;
    return (
      (ctx.request.headers['x-operator-user-role'] as string) ||
      body?.operator_user_role ||
      'parent'
    ); // 默认为家长角色
  }

  /**
   * 获取操作用户所属学校ID
   * @param ctx Koa上下文
   * @returns 学校ID
   */
  private getOperatorSchoolId(ctx: Context): string | undefined {
    const body = ctx.request.body as any;
    return (
      (ctx.request.headers['x-operator-school-id'] as string) ||
      body?.sso_school_id ||
      (ctx.request.query?.sso_school_id as string)
    );
  }

  /**
   * 解析操作信息
   * @param ctx Koa上下文
   * @returns 操作信息
   */
  private parseOperationInfo(ctx: Context): {
    module: OperationModule;
    operationType: OperationType;
    description: string;
    targetId?: string;
    targetType?: string;
  } {
    const path = ctx.request.path;
    const method = ctx.request.method;

    // 问卷管理模块
    if (path.startsWith('/api/questionnaire')) {
      return this.parseQuestionnaireOperation(ctx, method, path);
    }

    // 问卷填写模块
    if (path.startsWith('/api/response')) {
      return this.parseResponseOperation(ctx, method, path);
    }

    // 统计分析模块
    if (path.startsWith('/api/statistics')) {
      return this.parseStatisticsOperation(ctx, method, path);
    }

    // 默认系统操作
    return {
      module: OperationModule.SYSTEM,
      operationType:
        method === 'GET' ? OperationType.QUERY : OperationType.UPDATE,
      description: `${method} ${path}`,
      targetId: undefined,
      targetType: 'api',
    };
  }

  /**
   * 解析问卷操作信息
   */
  private parseQuestionnaireOperation(
    ctx: Context,
    method: string,
    path: string
  ) {
    const pathParts = path.split('/');
    const questionnaireId = pathParts[3];

    let operationType: OperationType;
    let description: string;

    switch (method) {
      case 'POST':
        operationType = OperationType.CREATE;
        description = '创建问卷';
        break;
      case 'PUT':
        if (path.includes('/status')) {
          operationType = OperationType.PUBLISH;
          description = '更新问卷状态';
        } else {
          operationType = OperationType.UPDATE;
          description = '更新问卷';
        }
        break;
      case 'DELETE':
        operationType = OperationType.DELETE;
        description = '删除问卷';
        break;
      default:
        operationType = OperationType.QUERY;
        description = '查询问卷';
    }

    return {
      module: OperationModule.QUESTIONNAIRE,
      operationType,
      description,
      targetId: questionnaireId,
      targetType: 'questionnaire',
    };
  }

  /**
   * 解析响应操作信息
   */
  private parseResponseOperation(ctx: Context, method: string, path: string) {
    const pathParts = path.split('/');
    const responseId = pathParts[3];

    let operationType: OperationType;
    let description: string;

    if (method === 'POST') {
      operationType = OperationType.SUBMIT;
      description = '提交问卷响应';
    } else {
      operationType = OperationType.QUERY;
      description = '查询问卷响应';
    }

    return {
      module: OperationModule.RESPONSE,
      operationType,
      description,
      targetId:
        responseId || (ctx.request.body as any)?.questionnaire_id?.toString(),
      targetType: 'response',
    };
  }

  /**
   * 解析统计操作信息
   */
  private parseStatisticsOperation(ctx: Context, method: string, path: string) {
    let description: string;

    if (path.includes('/school')) {
      description = '查询学校统计数据';
    } else if (path.includes('/teacher')) {
      description = '查询教师统计数据';
    } else if (path.includes('/ranking')) {
      description = '查询教师排名';
    } else if (path.includes('/trend')) {
      description = '查询趋势分析';
    } else {
      description = '查询统计数据';
    }

    return {
      module: OperationModule.STATISTICS,
      operationType: OperationType.QUERY,
      description,
      targetId:
        (ctx.request.query?.sso_school_id as string) ||
        (ctx.request.query?.sso_teacher_id as string),
      targetType: 'statistics',
    };
  }

  /**
   * 获取请求参数
   * @param ctx Koa上下文
   * @returns 请求参数
   */
  private getRequestParams(ctx: Context): any {
    const params: any = {};

    // 获取query参数
    if (Object.keys(ctx.request.query).length > 0) {
      params.query = ctx.request.query;
    }

    // 获取body参数（排除敏感信息）
    if (
      ctx.request.body &&
      typeof ctx.request.body === 'object' &&
      Object.keys(ctx.request.body).length > 0
    ) {
      const body = { ...(ctx.request.body as any) };

      // 移除敏感信息
      delete body.password;
      delete body.token;
      delete body.secret;

      params.body = body;
    }

    // 获取路径参数
    if (ctx.params && Object.keys(ctx.params).length > 0) {
      params.params = ctx.params;
    }

    return Object.keys(params).length > 0 ? params : undefined;
  }
}
