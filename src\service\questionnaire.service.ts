import { Provide, Inject } from '@midwayjs/core';
import { InjectRepository } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { APIManager } from '../common/APIManager';
import { CustomError } from '../error/custom.error';
import {
  Questionnaire,
  QuestionnaireStatus,
} from '../entity/questionnaire.entity';
import {
  CreateQuestionnaireDTO,
  UpdateQuestionnaireStatusDTO,
  QueryQuestionnaireDTO,
} from '../dto/questionnaire.dto';
import { IQuestionnaireListResponse, ISSoSchoolInfo } from '../interface';

@Provide()
export class QuestionnaireService {
  @InjectRepository(Questionnaire)
  questionnaireRepository: Repository<Questionnaire>;

  @Inject()
  apiManager: APIManager;

  /**
   * 验证SSO学校ID是否有效
   * @param ssoSchoolId SSO学校ID
   * @returns 学校信息
   */
  private async validateSSOSchoolId(
    ssoSchoolId: string
  ): Promise<ISSoSchoolInfo> {
    try {
      // 调用SSO接口验证学校ID
      const schoolInfo = await this.apiManager.send({
        apiCode: 'GET_SCHOOL_INFO', // 假设这是获取学校信息的API代码
        query: { schoolId: ssoSchoolId },
      });

      if (!schoolInfo || schoolInfo.status !== 'active') {
        throw new CustomError('学校ID无效或学校状态异常');
      }

      return {
        id: schoolInfo.id,
        name: schoolInfo.name,
        status: schoolInfo.status,
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError('验证学校ID时发生错误：' + error.message);
    }
  }

  /**
   * 创建问卷
   * @param createDto 创建问卷DTO
   * @param creatorUserId 创建用户ID
   * @param creatorUserName 创建用户名称
   * @returns 创建的问卷
   */
  async createQuestionnaire(
    createDto: CreateQuestionnaireDTO,
    creatorUserId: string,
    creatorUserName?: string
  ): Promise<Questionnaire> {
    // 验证SSO学校ID
    const schoolInfo = await this.validateSSOSchoolId(createDto.sso_school_id);

    // 检查同一学校同一月份是否已存在问卷
    const existingQuestionnaire = await this.questionnaireRepository.findOne({
      where: {
        sso_school_id: createDto.sso_school_id,
        month: createDto.month,
      },
    });

    if (existingQuestionnaire) {
      throw new CustomError(`该学校在${createDto.month}月份已存在问卷`);
    }

    // 验证时间范围
    if (createDto.start_time && createDto.end_time) {
      if (createDto.start_time >= createDto.end_time) {
        throw new CustomError('问卷开始时间必须早于结束时间');
      }
    }

    // 创建问卷数据
    const questionnaireData = {
      ...createDto,
      sso_school_name: schoolInfo.name,
      creator_user_id: creatorUserId,
      creator_user_name: creatorUserName,
      status: QuestionnaireStatus.DRAFT,
    };

    return await this.questionnaireRepository.create(questionnaireData);
  }

  /**
   * 查询问卷列表
   * @param queryDto 查询条件DTO
   * @returns 问卷列表和分页信息
   */
  async getQuestionnaireList(
    queryDto: QueryQuestionnaireDTO
  ): Promise<IQuestionnaireListResponse> {
    const { sso_school_id, month, status, page = 1, limit = 10 } = queryDto;

    // 构建查询条件
    const where: any = {};
    if (sso_school_id) {
      where.sso_school_id = sso_school_id;
    }
    if (month) {
      where.month = month;
    }
    if (status) {
      where.status = status;
    }

    // 计算分页参数
    const offset = (page - 1) * limit;

    // 查询数据
    const { rows: list, count: total } =
      await this.questionnaireRepository.findAndCountAll({
        where,
        offset,
        limit,
        order: [['created_at', 'DESC']],
      });

    return {
      list,
      total,
      page,
      limit,
    };
  }

  /**
   * 根据ID获取问卷详情
   * @param id 问卷ID
   * @returns 问卷详情
   */
  async getQuestionnaireById(id: number): Promise<Questionnaire> {
    const questionnaire = await this.questionnaireRepository.findByPk(id);
    if (!questionnaire) {
      throw new CustomError('问卷不存在');
    }
    return questionnaire;
  }

  /**
   * 更新问卷状态
   * @param id 问卷ID
   * @param updateDto 更新状态DTO
   * @param operatorUserId 操作用户ID
   * @returns 更新后的问卷
   */
  async updateQuestionnaireStatus(
    id: number,
    updateDto: UpdateQuestionnaireStatusDTO,
    operatorUserId: string
  ): Promise<Questionnaire> {
    const questionnaire = await this.getQuestionnaireById(id);

    // 验证状态转换是否合法
    this.validateStatusTransition(questionnaire.status, updateDto.status);

    // 更新状态
    await this.questionnaireRepository.update(
      { status: updateDto.status },
      { where: { id } }
    );

    // 返回更新后的问卷
    return await this.getQuestionnaireById(id);
  }

  /**
   * 验证问卷状态转换是否合法
   * @param currentStatus 当前状态
   * @param newStatus 新状态
   */
  private validateStatusTransition(
    currentStatus: QuestionnaireStatus,
    newStatus: QuestionnaireStatus
  ): void {
    const validTransitions: Record<QuestionnaireStatus, QuestionnaireStatus[]> =
      {
        [QuestionnaireStatus.DRAFT]: [
          QuestionnaireStatus.PUBLISHED,
          QuestionnaireStatus.CLOSED,
        ],
        [QuestionnaireStatus.PUBLISHED]: [QuestionnaireStatus.CLOSED],
        [QuestionnaireStatus.CLOSED]: [], // 已关闭的问卷不能再改变状态
      };

    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new CustomError(`不能从${currentStatus}状态转换到${newStatus}状态`);
    }
  }

  /**
   * 删除问卷（仅限草稿状态）
   * @param id 问卷ID
   * @param operatorUserId 操作用户ID
   */
  async deleteQuestionnaire(id: number, operatorUserId: string): Promise<void> {
    const questionnaire = await this.getQuestionnaireById(id);

    // 只有草稿状态的问卷才能删除
    if (questionnaire.status !== QuestionnaireStatus.DRAFT) {
      throw new CustomError('只有草稿状态的问卷才能删除');
    }

    await this.questionnaireRepository.destroy({ where: { id } });
  }
}
