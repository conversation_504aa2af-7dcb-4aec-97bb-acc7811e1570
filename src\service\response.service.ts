import { Provide, Inject } from '@midwayjs/core';
import { InjectRepository, InjectDataSource } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { APIManager } from '../common/APIManager';
import { CustomError } from '../error/custom.error';
import { Response } from '../entity/response.entity';
import { Answer } from '../entity/answer.entity';
import { Questionnaire, StarMode } from '../entity/questionnaire.entity';
import { SubmitResponseDTO, QueryResponseDTO } from '../dto/response.dto';
import { IQuestionnaireListResponse } from '../interface';

// SSO相关接口定义
interface ISSoStudentInfo {
  id: string;
  name: string;
  class: string;
  grade: string;
  school_id: string;
  status: string;
}

interface ISSoTeacherInfo {
  id: string;
  name: string;
  subject: string;
  position: string;
  department: string;
  school_id: string;
  status: string;
}

@Provide()
export class ResponseService {
  @InjectRepository(Response)
  responseRepository: Repository<Response>;

  @InjectRepository(Answer)
  answerRepository: Repository<Answer>;

  @InjectRepository(Questionnaire)
  questionnaireRepository: Repository<Questionnaire>;

  @Inject()
  apiManager: APIManager;

  @InjectDataSource()
  sequelize: Sequelize;

  /**
   * 验证SSO学生ID是否有效
   * @param ssoStudentId SSO学生ID
   * @returns 学生信息
   */
  private async validateSSOStudentId(
    ssoStudentId: string
  ): Promise<ISSoStudentInfo> {
    try {
      const studentInfo = await this.apiManager.send({
        apiCode: 'GET_STUDENT_INFO', // 假设这是获取学生信息的API代码
        query: { studentId: ssoStudentId },
      });

      if (!studentInfo || studentInfo.status !== 'active') {
        throw new CustomError('学生ID无效或学生状态异常');
      }

      return {
        id: studentInfo.id,
        name: studentInfo.name,
        class: studentInfo.class,
        grade: studentInfo.grade,
        school_id: studentInfo.school_id,
        status: studentInfo.status,
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError('验证学生ID时发生错误：' + error.message);
    }
  }

  /**
   * 验证SSO教师ID是否有效
   * @param ssoTeacherId SSO教师ID
   * @returns 教师信息
   */
  private async validateSSOTeacherId(
    ssoTeacherId: string
  ): Promise<ISSoTeacherInfo> {
    try {
      const teacherInfo = await this.apiManager.send({
        apiCode: 'GET_TEACHER_INFO', // 假设这是获取教师信息的API代码
        query: { teacherId: ssoTeacherId },
      });

      if (!teacherInfo || teacherInfo.status !== 'active') {
        throw new CustomError(`教师ID ${ssoTeacherId} 无效或教师状态异常`);
      }

      return {
        id: teacherInfo.id,
        name: teacherInfo.name,
        subject: teacherInfo.subject,
        position: teacherInfo.position,
        department: teacherInfo.department,
        school_id: teacherInfo.school_id,
        status: teacherInfo.status,
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(
        `验证教师ID ${ssoTeacherId} 时发生错误：` + error.message
      );
    }
  }

  /**
   * 将星级评分转换为百分制分数
   * @param rating 星级评分
   * @param starMode 星级模式（5星或10星）
   * @returns 百分制分数
   */
  private convertRatingToScore(rating: number, starMode: StarMode): number {
    if (starMode === StarMode.FIVE_STAR) {
      // 5星模式：1星=20分，2星=40分，3星=60分，4星=80分，5星=100分
      return rating * 20;
    } else {
      // 10星模式：1星=10分，2星=20分，...，10星=100分
      return rating * 10;
    }
  }

  /**
   * 计算总平均分
   * @param teacherEvaluations 教师评价数组
   * @param starMode 星级模式
   * @returns 总平均分（百分制）
   */
  private calculateTotalAverageScore(
    teacherEvaluations: any[],
    starMode: StarMode
  ): number {
    if (!teacherEvaluations || teacherEvaluations.length === 0) {
      return 0;
    }

    const totalScore = teacherEvaluations.reduce((sum, evaluation) => {
      return sum + this.convertRatingToScore(evaluation.rating, starMode);
    }, 0);

    return Math.round((totalScore / teacherEvaluations.length) * 100) / 100; // 保留两位小数
  }

  /**
   * 提交问卷响应
   * @param submitDto 提交响应DTO
   * @returns 创建的响应记录
   */
  async submitResponse(submitDto: SubmitResponseDTO): Promise<Response> {
    // 开启事务
    const transaction: Transaction = await this.sequelize.transaction();

    try {
      // 1. 验证问卷是否存在且可填写
      const questionnaire = await this.questionnaireRepository.findByPk(
        submitDto.questionnaire_id
      );
      if (!questionnaire) {
        throw new CustomError('问卷不存在');
      }

      if (questionnaire.status !== 'published') {
        throw new CustomError('问卷未发布，无法填写');
      }

      // 检查问卷时间范围
      const now = new Date();
      if (questionnaire.start_time && now < questionnaire.start_time) {
        throw new CustomError('问卷尚未开始');
      }
      if (questionnaire.end_time && now > questionnaire.end_time) {
        throw new CustomError('问卷已结束');
      }

      // 2. 验证月份是否匹配
      if (questionnaire.month !== submitDto.month) {
        throw new CustomError('提交的月份与问卷月份不匹配');
      }

      // 3. 检查是否已存在重复提交
      const existingResponse = await this.responseRepository.findOne({
        where: {
          parent_phone: submitDto.parent_phone,
          questionnaire_id: submitDto.questionnaire_id,
          sso_student_id: submitDto.sso_student_id,
          month: submitDto.month,
        },
        transaction,
      });

      if (existingResponse) {
        throw new CustomError('该家长已为此学生在本月提交过问卷，请勿重复提交');
      }

      // 4. 验证SSO学生ID
      const studentInfo = await this.validateSSOStudentId(
        submitDto.sso_student_id
      );

      // 5. 验证所有教师ID并获取教师信息
      const teacherInfos: ISSoTeacherInfo[] = [];
      for (const evaluation of submitDto.teacher_evaluations) {
        const teacherInfo = await this.validateSSOTeacherId(
          evaluation.sso_teacher_id
        );
        teacherInfos.push(teacherInfo);
      }

      // 6. 检查教师数量限制
      if (
        questionnaire.max_teachers_limit > 0 &&
        submitDto.teacher_evaluations.length > questionnaire.max_teachers_limit
      ) {
        throw new CustomError(
          `最多只能评价${questionnaire.max_teachers_limit}位教师`
        );
      }

      // 7. 验证评分范围
      const maxRating = questionnaire.star_mode;
      for (const evaluation of submitDto.teacher_evaluations) {
        if (evaluation.rating > maxRating) {
          throw new CustomError(`评分不能超过${maxRating}星`);
        }
      }

      // 8. 计算总平均分
      const totalAverageScore = this.calculateTotalAverageScore(
        submitDto.teacher_evaluations,
        questionnaire.star_mode
      );

      // 9. 创建响应主记录
      const responseData = {
        questionnaire_id: submitDto.questionnaire_id,
        parent_phone: submitDto.parent_phone,
        parent_name: submitDto.parent_name,
        sso_student_id: submitDto.sso_student_id,
        sso_student_name: studentInfo.name,
        sso_student_class: studentInfo.class,
        sso_student_grade: studentInfo.grade,
        month: submitDto.month,
        school_rating: submitDto.school_rating,
        school_description: submitDto.school_description,
        total_average_score: totalAverageScore,
        teacher_count: submitDto.teacher_evaluations.length,
        is_completed: true,
        submission_ip: '', // 可以从请求中获取IP
        remarks: submitDto.remarks,
      };

      const response = await this.responseRepository.create(responseData, {
        transaction,
      });

      // 10. 批量创建答案记录
      const answerData = submitDto.teacher_evaluations.map(
        (evaluation, index) => {
          const teacherInfo = teacherInfos[index];
          return {
            response_id: response.id,
            sso_teacher_id: evaluation.sso_teacher_id,
            sso_teacher_name: teacherInfo.name,
            sso_teacher_subject: teacherInfo.subject,
            sso_teacher_position: teacherInfo.position,
            sso_teacher_department: teacherInfo.department,
            rating: evaluation.rating,
            description: evaluation.description,
            tags: evaluation.tags,
            is_recommended: evaluation.is_recommended,
            teaching_quality_rating: evaluation.teaching_quality_rating,
            teaching_attitude_rating: evaluation.teaching_attitude_rating,
            classroom_management_rating: evaluation.classroom_management_rating,
            communication_rating: evaluation.communication_rating,
            professional_knowledge_rating:
              evaluation.professional_knowledge_rating,
            improvement_suggestions: evaluation.improvement_suggestions,
            most_satisfied_aspect: evaluation.most_satisfied_aspect,
            needs_improvement_aspect: evaluation.needs_improvement_aspect,
          };
        }
      );

      await this.answerRepository.bulkCreate(answerData, { transaction });

      // 提交事务
      await transaction.commit();

      // 返回完整的响应记录（包含关联的答案）
      return await this.responseRepository.findByPk(response.id, {
        include: [Answer],
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 查询响应列表
   * @param queryDto 查询条件DTO
   * @returns 响应列表和分页信息
   */
  async getResponseList(
    queryDto: QueryResponseDTO
  ): Promise<IQuestionnaireListResponse> {
    const {
      questionnaire_id,
      parent_phone,
      sso_student_id,
      month,
      is_completed,
      page = 1,
      limit = 10,
    } = queryDto;

    // 构建查询条件
    const where: any = {};
    if (questionnaire_id) {
      where.questionnaire_id = questionnaire_id;
    }
    if (parent_phone) {
      where.parent_phone = parent_phone;
    }
    if (sso_student_id) {
      where.sso_student_id = sso_student_id;
    }
    if (month) {
      where.month = month;
    }
    if (typeof is_completed === 'boolean') {
      where.is_completed = is_completed;
    }

    // 计算分页参数
    const offset = (page - 1) * limit;

    // 查询数据
    const { rows: list, count: total } =
      await this.responseRepository.findAndCountAll({
        where,
        include: [
          {
            model: Answer,
            as: 'answers',
          },
        ],
        offset,
        limit,
        order: [['created_at', 'DESC']],
      });

    return {
      list,
      total,
      page,
      limit,
    };
  }

  /**
   * 根据ID获取响应详情
   * @param id 响应ID
   * @returns 响应详情
   */
  async getResponseById(id: number): Promise<Response> {
    const response = await this.responseRepository.findByPk(id, {
      include: [
        {
          model: Answer,
          as: 'answers',
        },
      ],
    });

    if (!response) {
      throw new CustomError('响应记录不存在');
    }

    return response;
  }

  /**
   * 检查是否已提交响应
   * @param parentPhone 家长手机号
   * @param questionnaireId 问卷ID
   * @param studentId 学生ID
   * @param month 月份
   * @returns 是否已提交
   */
  async checkResponseExists(
    parentPhone: string,
    questionnaireId: number,
    studentId: string,
    month: string
  ): Promise<boolean> {
    const existingResponse = await this.responseRepository.findOne({
      where: {
        parent_phone: parentPhone,
        questionnaire_id: questionnaireId,
        sso_student_id: studentId,
        month: month,
      },
    });

    return !!existingResponse;
  }

  /**
   * 获取问卷统计信息
   * @param questionnaireId 问卷ID
   * @returns 统计信息
   */
  async getQuestionnaireStatistics(questionnaireId: number) {
    // 验证问卷是否存在
    const questionnaire = await this.questionnaireRepository.findByPk(
      questionnaireId
    );
    if (!questionnaire) {
      throw new CustomError('问卷不存在');
    }

    // 统计响应数量
    const totalResponses = await this.responseRepository.count({
      where: { questionnaire_id: questionnaireId },
    });

    const completedResponses = await this.responseRepository.count({
      where: {
        questionnaire_id: questionnaireId,
        is_completed: true,
      },
    });

    // 计算完成率
    const completionRate =
      totalResponses > 0
        ? Math.round((completedResponses / totalResponses) * 100 * 100) / 100
        : 0;

    // 计算平均评分
    const avgScoreResult = await this.responseRepository.findOne({
      where: { questionnaire_id: questionnaireId },
      attributes: [
        [
          this.sequelize.fn('AVG', this.sequelize.col('total_average_score')),
          'average_rating',
        ],
      ],
      raw: true,
    });

    const averageRating = avgScoreResult
      ? Math.round((avgScoreResult as any).average_rating * 100) / 100
      : 0;

    // 统计教师评价数量
    const teacherEvaluationCount = await this.answerRepository.count({
      include: [
        {
          model: Response,
          where: { questionnaire_id: questionnaireId },
        },
      ],
    });

    return {
      questionnaire_id: questionnaireId,
      total_responses: totalResponses,
      completed_responses: completedResponses,
      completion_rate: completionRate,
      average_rating: averageRating,
      teacher_evaluation_count: teacherEvaluationCount,
    };
  }
}
